<?php
/**
 * 意见反馈API
 * 提供意见反馈的提交和管理功能
 */

// 设置响应头
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS预检请求
if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// 引入数据库配置
require_once __DIR__ . '/../includes/config.php';

try {
    // 获取action参数，支持GET、POST和JSON body
    $action = $_GET['action'] ?? $_POST['action'] ?? '';

    // 如果没有找到action，尝试从JSON body中获取
    if (empty($action)) {
        $input = json_decode(file_get_contents('php://input'), true);
        $action = $input['action'] ?? '';
    }

    switch ($action) {
        case 'submit_feedback':
            handleSubmitFeedback();
            break;

        case 'get_feedback_list':
            handleGetFeedbackList();
            break;

        case 'get_feedback_detail':
            handleGetFeedbackDetail();
            break;

        case 'update_feedback':
            handleUpdateFeedback();
            break;

        case 'get_feedback_stats':
            handleGetFeedbackStats();
            break;

        case 'delete_feedback':
            handleDeleteFeedback();
            break;

        case 'get_violations_list':
            handleGetViolationsList();
            break;

        case 'delete_violation':
            handleDeleteViolation();
            break;

        case 'clear_all_violations':
            handleClearAllViolations();
            break;

        case 'get_sensitive_words':
            handleGetSensitiveWords();
            break;

        case 'add_sensitive_word':
            handleAddSensitiveWord();
            break;

        case 'delete_sensitive_word':
            handleDeleteSensitiveWord();
            break;

        default:
            throw new Exception('无效的操作');
    }

} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 敏感词过滤配置
 */
function getSensitiveWords() {
    global $pdo;

    try {
        $stmt = $pdo->query("SELECT word FROM sensitive_words WHERE status = 'active'");
        $words = $stmt->fetchAll(PDO::FETCH_COLUMN);

        // 如果数据库中没有敏感词，返回默认的敏感词列表
        if (empty($words)) {
            return getDefaultSensitiveWords();
        }

        return $words;
    } catch (Exception $e) {
        // 如果数据库查询失败，返回默认的敏感词列表
        return getDefaultSensitiveWords();
    }
}

/**
 * 默认敏感词列表（用于初始化或数据库查询失败时）
 */
function getDefaultSensitiveWords() {
    return [
        // 暴力相关
        '杀死', '杀害', '暴力', '打死', '弄死', '干死', '血腥', '残忍', '虐待', '折磨',

        // 脏话粗口
        '操', '草', '妈的', '他妈', '你妈', '傻逼', '傻B', '白痴', '智障', '脑残',
        '滚', '滚蛋', '去死', '死去', '找死', '该死', '混蛋', '王八蛋', '狗东西',

        // 非法活动
        '毒品', '吸毒', '贩毒', '制毒', '海洛因', '冰毒', '摇头丸', '大麻',
        '赌博', '赌场', '博彩', '六合彩', '私彩', '黑彩',
        '诈骗', '传销', '非法集资', '洗钱', '走私', '偷税', '逃税',

        // 色情相关
        '色情', '黄色', '淫秽', '裸体', '性交', '做爱', '嫖娼', '卖淫',

        // 政治敏感
        '法轮功', '邪教', '反政府', '颠覆', '暴动', '游行示威',

        // 其他不当内容
        '自杀', '自残', '跳楼', '割腕', '仇恨', '歧视', '种族主义'
    ];
}

/**
 * 检查文本是否包含敏感词
 */
function checkSensitiveWords($text) {
    $sensitiveWords = getSensitiveWords();
    $foundWords = [];

    foreach ($sensitiveWords as $word) {
        if (stripos($text, $word) !== false) {
            $foundWords[] = $word;
        }
    }

    return $foundWords;
}

/**
 * 记录违规尝试
 */
function logViolationAttempt($userId, $content, $sensitiveWords, $userInfo = []) {
    global $pdo;

    try {
        $stmt = $pdo->prepare("
            INSERT INTO feedback_violations
            (user_id, content, sensitive_words, user_info, created_at)
            VALUES (?, ?, ?, ?, NOW())
        ");

        $stmt->execute([
            $userId,
            $content,
            json_encode($sensitiveWords, JSON_UNESCAPED_UNICODE),
            json_encode($userInfo, JSON_UNESCAPED_UNICODE)
        ]);
    } catch (Exception $e) {
        // 记录失败不影响主流程，只记录错误日志
        error_log("记录违规尝试失败: " . $e->getMessage());
    }
}

/**
 * 提交意见反馈
 */
function handleSubmitFeedback() {
    global $pdo;

    $input = json_decode(file_get_contents('php://input'), true);

    $feedbackType = $input['feedback_type'] ?? '';
    $content = $input['content'] ?? '';
    $contactInfo = $input['contact_info'] ?? '';
    $wechatAuth = $input['wechat_auth'] ?? false;
    $userInfo = $input['user_info'] ?? null;

    // 验证必填字段
    if (empty($feedbackType) || empty($content)) {
        throw new Exception('反馈类型和内容不能为空');
    }

    // 验证反馈类型
    $validTypes = ['建议', '问题反馈', '功能需求', '其他'];
    if (!in_array($feedbackType, $validTypes)) {
        throw new Exception('无效的反馈类型');
    }

    // 验证内容长度
    if (mb_strlen($content) > 500) {
        throw new Exception('反馈内容不能超过500字');
    }

    // 敏感词检查
    $sensitiveWords = checkSensitiveWords($content);
    if (!empty($sensitiveWords)) {
        // 记录违规尝试
        logViolationAttempt(
            $userId,
            $content,
            $sensitiveWords,
            [
                'wechat_openid' => $wechatOpenid,
                'username' => $username,
                'real_name' => $realName,
                'contact_info' => $contactInfo,
                'feedback_type' => $feedbackType,
                'ip_address' => $_SERVER['REMOTE_ADDR'] ?? '',
                'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? ''
            ]
        );

        throw new Exception('反馈内容包含不当词汇，请修改后重新提交');
    }

    // 准备插入数据
    $userId = null;
    $wechatOpenid = null;
    $username = null;
    $realName = null;

    if ($wechatAuth && $userInfo) {
        $userId = $userInfo['id'] ?? null;
        $wechatOpenid = $userInfo['openid'] ?? null;
        $username = $userInfo['username'] ?? null;
        $realName = $userInfo['real_name'] ?? null;
    }

    // 插入数据库
    $stmt = $pdo->prepare("
        INSERT INTO feedback (
            user_id, wechat_openid, username, real_name,
            feedback_type, content, contact_info, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())
    ");

    $stmt->execute([
        $userId, $wechatOpenid, $username, $realName,
        $feedbackType, $content, $contactInfo
    ]);

    $feedbackId = $pdo->lastInsertId();

    echo json_encode([
        'success' => true,
        'message' => '反馈提交成功',
        'data' => [
            'feedback_id' => $feedbackId,
            'created_at' => date('Y-m-d H:i:s')
        ]
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 获取反馈列表（管理员）
 */
function handleGetFeedbackList() {
    global $pdo;

    // 验证管理员权限
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
        throw new Exception('权限不足');
    }

    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = min(100, max(1, intval($_GET['limit'] ?? 20)));
    $status = $_GET['status'] ?? '';
    $type = $_GET['type'] ?? '';
    $search = $_GET['search'] ?? '';

    $offset = ($page - 1) * $limit;

    // 构建查询条件
    $where = [];
    $params = [];

    if ($status) {
        $where[] = "status = ?";
        $params[] = $status;
    }

    if ($type) {
        $where[] = "feedback_type = ?";
        $params[] = $type;
    }

    if ($search) {
        $where[] = "(content LIKE ? OR contact_info LIKE ? OR username LIKE ? OR real_name LIKE ?)";
        $searchTerm = "%{$search}%";
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
        $params[] = $searchTerm;
    }

    $whereClause = $where ? 'WHERE ' . implode(' AND ', $where) : '';

    // 获取总数
    $countSql = "SELECT COUNT(*) FROM feedback {$whereClause}";
    $countStmt = $pdo->prepare($countSql);
    $countStmt->execute($params);
    $total = $countStmt->fetchColumn();

    // 获取列表
    $sql = "
        SELECT id, user_id, username, real_name, feedback_type, content,
               contact_info, status, read_at, processed_at,
               created_at, updated_at
        FROM feedback
        {$whereClause}
        ORDER BY created_at DESC
        LIMIT {$limit} OFFSET {$offset}
    ";

    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $list = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 格式化数据
    foreach ($list as &$item) {
        $item['status_text'] = getStatusText($item['status']);
        $item['user_info'] = [
            'id' => $item['user_id'],
            'username' => $item['username'],
            'real_name' => $item['real_name']
        ];
        unset($item['user_id'], $item['username'], $item['real_name']);
    }

    echo json_encode([
        'success' => true,
        'data' => [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'total_pages' => ceil($total / $limit)
        ]
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 获取反馈详情（管理员）
 */
function handleGetFeedbackDetail() {
    global $pdo;

    // 验证管理员权限
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
        throw new Exception('权限不足');
    }

    $feedbackId = $_GET['feedback_id'] ?? '';
    if (!$feedbackId) {
        throw new Exception('反馈ID不能为空');
    }

    $stmt = $pdo->prepare("
        SELECT id, user_id, username, real_name, feedback_type, content,
               contact_info, status, read_at, processed_at,
               created_at, updated_at
        FROM feedback
        WHERE id = ?
    ");

    $stmt->execute([$feedbackId]);
    $feedback = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$feedback) {
        throw new Exception('反馈不存在');
    }

    // 自动标记为已读
    if ($feedback['status'] === 'unread') {
        $updateStmt = $pdo->prepare("UPDATE feedback SET status = 'read', read_at = NOW(), updated_at = NOW() WHERE id = ?");
        $updateStmt->execute([$feedbackId]);
        $feedback['status'] = 'read';
        $feedback['read_at'] = date('Y-m-d H:i:s');
    }

    // 格式化数据
    $feedback['status_text'] = getStatusText($feedback['status']);
    $feedback['user_info'] = [
        'id' => $feedback['user_id'],
        'username' => $feedback['username'],
        'real_name' => $feedback['real_name']
    ];
    unset($feedback['user_id'], $feedback['username'], $feedback['real_name']);

    echo json_encode([
        'success' => true,
        'data' => $feedback
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 更新反馈状态（管理员）
 */
function handleUpdateFeedback() {
    global $pdo;

    // 验证管理员权限
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
        throw new Exception('权限不足');
    }

    $adminInfo = $_SESSION['user'];
    $input = json_decode(file_get_contents('php://input'), true);

    $feedbackId = $input['feedback_id'] ?? '';
    $action = $input['action_type'] ?? ''; // 'mark_processed' 或其他操作

    if (!$feedbackId) {
        throw new Exception('反馈ID不能为空');
    }

    if ($action === 'mark_processed') {
        // 标记为已处理
        $sql = "UPDATE feedback SET status = 'processed', processed_at = NOW(), updated_at = NOW() WHERE id = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$feedbackId]);

        $message = '反馈已标记为已处理';
    } else {
        throw new Exception('无效的操作类型');
    }

    echo json_encode([
        'success' => true,
        'message' => $message,
        'data' => [
            'feedback_id' => $feedbackId,
            'updated_at' => date('Y-m-d H:i:s')
        ]
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 获取反馈统计（管理员）
 */
function handleGetFeedbackStats() {
    global $pdo;

    // 验证管理员权限
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
        throw new Exception('权限不足');
    }

    // 总体统计
    $stmt = $pdo->query("
        SELECT
            COUNT(*) as total,
            SUM(CASE WHEN status = 'unread' THEN 1 ELSE 0 END) as unread,
            SUM(CASE WHEN status = 'read' THEN 1 ELSE 0 END) as `read`,
            SUM(CASE WHEN status = 'processed' THEN 1 ELSE 0 END) as processed
        FROM feedback
    ");
    $stats = $stmt->fetch(PDO::FETCH_ASSOC);

    // 按类型统计
    $stmt = $pdo->query("
        SELECT feedback_type, COUNT(*) as count
        FROM feedback
        GROUP BY feedback_type
    ");
    $typeStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $byType = [];
    foreach ($typeStats as $item) {
        $byType[$item['feedback_type']] = intval($item['count']);
    }

    // 按状态统计（详细）
    $stmt = $pdo->query("
        SELECT status, COUNT(*) as count
        FROM feedback
        GROUP BY status
    ");
    $statusDetailStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $byStatus = [];
    foreach ($statusDetailStats as $item) {
        $byStatus[$item['status']] = intval($item['count']);
    }

    // 最近7天和30天统计
    $stmt = $pdo->query("
        SELECT
            SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN 1 ELSE 0 END) as recent_7_days,
            SUM(CASE WHEN created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 ELSE 0 END) as recent_30_days
        FROM feedback
    ");
    $recentStats = $stmt->fetch(PDO::FETCH_ASSOC);

    echo json_encode([
        'success' => true,
        'data' => [
            'total' => intval($stats['total']),
            'unread' => intval($stats['unread']),
            'read' => intval($stats['read']),
            'processed' => intval($stats['processed']),
            'by_type' => $byType,
            'by_status' => $byStatus,
            'recent_7_days' => intval($recentStats['recent_7_days']),
            'recent_30_days' => intval($recentStats['recent_30_days'])
        ]
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 删除反馈（管理员）
 */
function handleDeleteFeedback() {
    global $pdo;

    // 验证管理员权限
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
        throw new Exception('权限不足');
    }

    $input = json_decode(file_get_contents('php://input'), true);

    $feedbackId = $input['feedback_id'] ?? '';
    if (!$feedbackId) {
        throw new Exception('反馈ID不能为空');
    }

    $stmt = $pdo->prepare("DELETE FROM feedback WHERE id = ?");
    $stmt->execute([$feedbackId]);

    echo json_encode([
        'success' => true,
        'message' => '删除成功'
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 验证管理员Token
 */
function verifyAdminToken($token) {
    global $pdo;

    // 如果token为空，检查session
    if (empty($token) || $token === 'admin_token_placeholder') {
        // 检查session是否存在且为管理员
        if (isset($_SESSION['user']) && $_SESSION['user']['role'] === 'admin') {
            return [
                'user_id' => $_SESSION['user']['id'],
                'username' => $_SESSION['user']['username'],
                'role' => $_SESSION['user']['role'],
                'real_name' => $_SESSION['user']['real_name'] ?? $_SESSION['user']['username']
            ];
        }
        return false;
    }

    try {
        $stmt = $pdo->prepare("
            SELECT wt.user_id, u.username, u.role, u.real_name
            FROM wechat_tokens wt
            JOIN users u ON wt.user_id = u.id
            WHERE wt.token = ? AND wt.expires_at > NOW()
        ");
        $stmt->execute([$token]);
        $result = $stmt->fetch();

        if ($result && $result['role'] === 'admin') {
            return $result;
        }

        return false;
    } catch (Exception $e) {
        return false;
    }
}

/**
 * 获取状态文本
 */
function getStatusText($status) {
    $statusMap = [
        'unread' => '未读',
        'read' => '已读',
        'processed' => '已处理'
    ];
    return $statusMap[$status] ?? $status;
}

/**
 * 获取违规记录列表
 */
function handleGetViolationsList() {
    global $pdo;

    // 验证管理员权限
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
        throw new Exception('权限不足');
    }

    $page = intval($_GET['page'] ?? 1);
    $limit = intval($_GET['limit'] ?? 10);
    $offset = ($page - 1) * $limit;

    // 获取总数
    $countStmt = $pdo->query("SELECT COUNT(*) FROM feedback_violations");
    $total = $countStmt->fetchColumn();

    // 获取列表数据
    $stmt = $pdo->prepare("
        SELECT * FROM feedback_violations
        ORDER BY created_at DESC
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([$limit, $offset]);
    $violations = $stmt->fetchAll();

    echo json_encode([
        'success' => true,
        'data' => [
            'list' => $violations,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ]
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 删除违规记录
 */
function handleDeleteViolation() {
    global $pdo;

    // 验证管理员权限
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
        throw new Exception('权限不足');
    }

    $input = json_decode(file_get_contents('php://input'), true);
    $violationId = $input['violation_id'] ?? '';

    if (!$violationId) {
        throw new Exception('违规记录ID不能为空');
    }

    $stmt = $pdo->prepare("DELETE FROM feedback_violations WHERE id = ?");
    $stmt->execute([$violationId]);

    echo json_encode([
        'success' => true,
        'message' => '删除成功'
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 清空所有违规记录
 */
function handleClearAllViolations() {
    global $pdo;

    // 验证管理员权限
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
        throw new Exception('权限不足');
    }

    $stmt = $pdo->prepare("DELETE FROM feedback_violations");
    $stmt->execute();

    echo json_encode([
        'success' => true,
        'message' => '清空成功'
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 获取敏感词列表
 */
function handleGetSensitiveWords() {
    global $pdo;

    // 验证管理员权限
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
        throw new Exception('权限不足');
    }

    $stmt = $pdo->query("SELECT * FROM sensitive_words ORDER BY category, word");
    $words = $stmt->fetchAll();

    echo json_encode([
        'success' => true,
        'data' => $words
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 添加敏感词
 */
function handleAddSensitiveWord() {
    global $pdo;

    // 验证管理员权限
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
        throw new Exception('权限不足');
    }

    $input = json_decode(file_get_contents('php://input'), true);
    $word = trim($input['word'] ?? '');
    $category = $input['category'] ?? '其他';

    if (!$word) {
        throw new Exception('敏感词不能为空');
    }

    // 检查是否已存在
    $checkStmt = $pdo->prepare("SELECT id FROM sensitive_words WHERE word = ?");
    $checkStmt->execute([$word]);
    if ($checkStmt->fetch()) {
        throw new Exception('该敏感词已存在');
    }

    $stmt = $pdo->prepare("INSERT INTO sensitive_words (word, category, created_at) VALUES (?, ?, NOW())");
    $stmt->execute([$word, $category]);

    echo json_encode([
        'success' => true,
        'message' => '添加成功'
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * 删除敏感词
 */
function handleDeleteSensitiveWord() {
    global $pdo;

    // 验证管理员权限
    if (!isset($_SESSION['user']) || $_SESSION['user']['role'] !== 'admin') {
        throw new Exception('权限不足');
    }

    $input = json_decode(file_get_contents('php://input'), true);
    $wordId = $input['word_id'] ?? '';

    if (!$wordId) {
        throw new Exception('敏感词ID不能为空');
    }

    $stmt = $pdo->prepare("DELETE FROM sensitive_words WHERE id = ?");
    $stmt->execute([$wordId]);

    echo json_encode([
        'success' => true,
        'message' => '删除成功'
    ], JSON_UNESCAPED_UNICODE);
}
?>
